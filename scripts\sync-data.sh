#!/bin/bash

# Script para sincronização de dados entre ambientes
# Wrapper para o script Node.js de sincronização

set -e  # Parar em caso de erro

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para mostrar ajuda
show_help() {
    echo -e "${BLUE}🔄 Script de Sincronização de Dados - Sem Fronteiras SSH${NC}"
    echo ""
    echo "USO:"
    echo "  ./sync-data.sh [opções]"
    echo ""
    echo "OPÇÕES:"
    echo "  -s, --source <ambiente>     Ambiente de origem (production|staging)"
    echo "  -t, --target <ambiente>     Ambiente de destino (production|staging)"
    echo "  -u, --include-users         Incluir usuários na sincronização"
    echo "  -h, --include-history       Incluir histórico de comandos"
    echo "  -d, --dry-run              Executar sem fazer alterações"
    echo "  -c, --conflict <resolução>  Como resolver conflitos (skip|overwrite|merge)"
    echo "  --help                     Mostrar esta ajuda"
    echo ""
    echo "EXEMPLOS:"
    echo "  # Dry-run de produção para homologação"
    echo "  ./sync-data.sh -s production -t staging -d"
    echo ""
    echo "  # Sincronizar tudo, sobrescrevendo conflitos"
    echo "  ./sync-data.sh -s production -t staging -u -h -c overwrite"
    echo ""
    echo "  # Sincronizar apenas servidores e comandos"
    echo "  ./sync-data.sh -s production -t staging"
    echo ""
}

# Função para detectar ambiente
detect_environment() {
    if [ -d "/var/www/sem-fronteiras-ssh" ]; then
        echo "production"
    else
        echo "development"
    fi
}

# Função para determinar o contêiner backend
get_backend_container() {
    local env=$(detect_environment)
    if [ "$env" = "production" ]; then
        echo "sem-fronteiras-ssh-backend-1"
    else
        echo "sem-fronteiras-backend-1"
    fi
}

# Função para verificar se o contêiner está rodando
check_container() {
    local container=$1
    if ! docker ps | grep -q "$container"; then
        echo -e "${RED}❌ Contêiner $container não está rodando${NC}"
        echo "Inicie os serviços com: docker-compose up -d"
        exit 1
    fi
}

# Função para verificar dependências
check_dependencies() {
    # Verificar se Docker está disponível
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker não está instalado ou não está no PATH${NC}"
        exit 1
    fi

    # Verificar se o contêiner backend está rodando
    local backend_container=$(get_backend_container)
    check_container "$backend_container"
    
    echo -e "${GREEN}✅ Dependências verificadas${NC}"
}

# Função para confirmar execução
confirm_execution() {
    local source=$1
    local target=$2
    local dry_run=$3
    
    if [ "$dry_run" = "true" ]; then
        echo -e "${YELLOW}🧪 Modo DRY-RUN ativado - nenhuma alteração será feita${NC}"
        return 0
    fi
    
    echo -e "${YELLOW}⚠️  ATENÇÃO: Esta operação irá sincronizar dados do ambiente ${source^^} para ${target^^}${NC}"
    echo ""
    echo "Esta operação pode modificar dados no ambiente de destino."
    echo ""
    
    # Em ambiente não-interativo, assumir confirmação
    if [ ! -t 0 ]; then
        echo "Ambiente não-interativo detectado. Prosseguindo..."
        return 0
    fi
    
    read -p "Deseja continuar? (s/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Ss]$ ]]; then
        echo -e "${RED}❌ Operação cancelada pelo usuário${NC}"
        exit 0
    fi
}

# Função principal
main() {
    # Valores padrão
    SOURCE=""
    TARGET=""
    INCLUDE_USERS=false
    INCLUDE_HISTORY=false
    DRY_RUN=false
    CONFLICT_RESOLUTION="skip"
    
    # Parsear argumentos
    while [[ $# -gt 0 ]]; do
        case $1 in
            -s|--source)
                SOURCE="$2"
                shift 2
                ;;
            -t|--target)
                TARGET="$2"
                shift 2
                ;;
            -u|--include-users)
                INCLUDE_USERS=true
                shift
                ;;
            -h|--include-history)
                INCLUDE_HISTORY=true
                shift
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -c|--conflict)
                CONFLICT_RESOLUTION="$2"
                shift 2
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                echo -e "${RED}❌ Opção desconhecida: $1${NC}"
                show_help
                exit 1
                ;;
        esac
    done
    
    # Validar argumentos obrigatórios
    if [ -z "$SOURCE" ] || [ -z "$TARGET" ]; then
        echo -e "${RED}❌ Argumentos --source e --target são obrigatórios${NC}"
        show_help
        exit 1
    fi
    
    # Validar valores dos argumentos
    if [[ ! "$SOURCE" =~ ^(production|staging)$ ]]; then
        echo -e "${RED}❌ Source deve ser 'production' ou 'staging'${NC}"
        exit 1
    fi
    
    if [[ ! "$TARGET" =~ ^(production|staging)$ ]]; then
        echo -e "${RED}❌ Target deve ser 'production' ou 'staging'${NC}"
        exit 1
    fi
    
    if [ "$SOURCE" = "$TARGET" ]; then
        echo -e "${RED}❌ Source e target não podem ser iguais${NC}"
        exit 1
    fi
    
    if [[ ! "$CONFLICT_RESOLUTION" =~ ^(skip|overwrite|merge)$ ]]; then
        echo -e "${RED}❌ Conflict resolution deve ser 'skip', 'overwrite' ou 'merge'${NC}"
        exit 1
    fi
    
    echo -e "${BLUE}🔄 Iniciando sincronização de dados...${NC}"
    echo ""
    
    # Verificar dependências
    check_dependencies
    
    # Mostrar configuração
    echo -e "${BLUE}📋 Configuração da sincronização:${NC}"
    echo "   Origem: ${SOURCE^^}"
    echo "   Destino: ${TARGET^^}"
    echo "   Incluir usuários: $([ "$INCLUDE_USERS" = true ] && echo "SIM" || echo "NÃO")"
    echo "   Incluir histórico: $([ "$INCLUDE_HISTORY" = true ] && echo "SIM" || echo "NÃO")"
    echo "   Modo dry-run: $([ "$DRY_RUN" = true ] && echo "SIM" || echo "NÃO")"
    echo "   Resolução de conflitos: ${CONFLICT_RESOLUTION^^}"
    echo ""
    
    # Confirmar execução
    confirm_execution "$SOURCE" "$TARGET" "$DRY_RUN"
    
    # Construir argumentos para o script Node.js
    local node_args="--source $SOURCE --target $TARGET"
    
    if [ "$INCLUDE_USERS" = true ]; then
        node_args="$node_args --include-users"
    fi
    
    if [ "$INCLUDE_HISTORY" = true ]; then
        node_args="$node_args --include-history"
    fi
    
    if [ "$DRY_RUN" = true ]; then
        node_args="$node_args --dry-run"
    fi
    
    node_args="$node_args --conflict-resolution $CONFLICT_RESOLUTION"
    
    # Executar sincronização
    local backend_container=$(get_backend_container)
    echo -e "${BLUE}🚀 Executando sincronização no contêiner $backend_container...${NC}"
    echo ""
    
    if docker exec "$backend_container" node scripts/sync-data.js $node_args; then
        echo ""
        echo -e "${GREEN}✅ Sincronização concluída com sucesso!${NC}"
    else
        echo ""
        echo -e "${RED}❌ Erro durante a sincronização${NC}"
        exit 1
    fi
}

# Executar apenas se chamado diretamente
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
